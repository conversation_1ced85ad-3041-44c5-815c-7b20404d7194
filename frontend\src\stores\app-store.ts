import { defineStore } from 'pinia';
import { Component, shallowRef, nextTick } from 'vue';

//Global app elements like header, layout, data cache, etc

export const useAppStore = defineStore('app', {
  state: () => ({
    storage: {
      usernameCache: {} as Record<string, string>,
    },
    headerExtensionComp: shallowRef<Component | null>(null),
    headerExtensionProps: {} as Record<string, any>,
    isBuyDisplay: true,
  }),
  getters: {
  },
  actions: {
    //TODO: This seems to cause (or the MainLayout's binding setup does) a 'ResizeObserver loop completed with undelivered notifications.' error.
    //      It doesn't seem to cause any issues, but be aware.
    setHeaderExtensionComp(comp: Component, props: Record<string, any> = {}) {
      this.headerExtensionComp = comp;
      this.headerExtensionProps = props;
    },
    clearHeaderExtensionComp() {
      this.headerExtensionComp = null;
      this.headerExtensionProps = {};
    },
  }
});
